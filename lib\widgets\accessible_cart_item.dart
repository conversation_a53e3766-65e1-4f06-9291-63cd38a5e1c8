import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart'; // Use Riverpod Consumer
import '../services/accessibility_service.dart';
import '../models/cart.dart';
import '../widgets/accessibility_widgets.dart';

/// 🛒 WCAG 2.1 AA Compliant Cart Item Widget
/// Provides accessible cart item management with:
/// - Screen reader support for item details and actions
/// - Keyboard navigation for quantity controls
/// - Live region announcements for cart updates
/// - High contrast mode compatibility
/// - Proper semantic structure for cart operations
class AccessibleCartItem extends ConsumerStatefulWidget { // Changed to ConsumerStatefulWidget
  final CartItem cartItem;
  final VoidCallback? onRemove;
  final Function(int)? onQuantityChanged;
  final VoidCallback? onEdit;

  const AccessibleCartItem({
    super.key,
    required this.cartItem,
    this.onRemove,
    this.onQuantityChanged,
    this.onEdit,
  });

  @override
  ConsumerState<AccessibleCartItem> createState() => _AccessibleCartItemState(); // Changed to ConsumerState
}

class _AccessibleCartItemState extends ConsumerState<AccessibleCartItem> { // Changed to ConsumerState
  bool _isFocused = false;

  @override
  Widget build(BuildContext context) {
    final accessibilityService = ref.watch(accessibilityServiceProvider); // Access via Riverpod
    return _buildCartItemCard(accessibilityService);
  }

  Widget _buildCartItemCard(AccessibilityService accessibilityService) {
    final theme = Theme.of(context);
    final totalPrice = widget.cartItem.itemPrice * widget.cartItem.quantity;
    
    // Build comprehensive semantic label
    String semanticLabel = _buildSemanticLabel(totalPrice);

    return Semantics(
      label: semanticLabel,
      child: Focus(
        onFocusChange: (focused) {
          setState(() {
            _isFocused = focused;
          });
          if (focused) {
            accessibilityService.announce(semanticLabel);
          }
        },
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          padding: accessibilityService.getRecommendedPadding(),
          decoration: _buildCardDecoration(theme, accessibilityService),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Item header with name and total price
              _buildItemHeader(theme, accessibilityService, totalPrice),
              
              const SizedBox(height: 12),
              
              // Item details and customizations
              _buildItemDetails(theme, accessibilityService),
              
              const SizedBox(height: 16),
              
              // Quantity controls and actions
              _buildItemActions(theme, accessibilityService),
            ],
          ),
        ),
      ),
    );
  }

  String _buildSemanticLabel(double totalPrice) {
    String label = '${widget.cartItem.displayName}, quantity ${widget.cartItem.quantity}';
    
    // Add individual price
    label += ', \$${widget.cartItem.itemPrice.toStringAsFixed(2)} each';
    
    // Add total price
    label += ', total \$${totalPrice.toStringAsFixed(2)}';
    
    // Add customizations if any
    if (widget.cartItem.customizations?.isNotEmpty == true) {
      label += ', with customizations: ${widget.cartItem.customizations!.values.expand((e) => e).map((e) => e.name).join(', ')}';
    }
    
    // Add special instructions if any
    if (widget.cartItem.specialInstructions?.isNotEmpty == true) {
      label += ', special instructions: ${widget.cartItem.specialInstructions}';
    }
    
    return label;
  }

  BoxDecoration _buildCardDecoration(ThemeData theme, AccessibilityService accessibilityService) {
    return BoxDecoration(
      color: theme.cardColor,
      borderRadius: BorderRadius.circular(12),
      border: Border.all(
        color: _isFocused 
            ? theme.primaryColor 
            : (accessibilityService.isHighContrastEnabled 
                ? theme.colorScheme.outline 
                : Colors.transparent),
        width: _isFocused ? 2 : (accessibilityService.isHighContrastEnabled ? 1 : 0),
      ),
      boxShadow: accessibilityService.isReduceMotionEnabled 
          ? null 
          : [
              BoxShadow(
                color: Colors.black.withOpacity(0.1), // Changed from withValues
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
    );
  }

  Widget _buildItemHeader(ThemeData theme, AccessibilityService accessibilityService, double totalPrice) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Item image placeholder
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(8),
            border: accessibilityService.isHighContrastEnabled
                ? Border.all(color: theme.colorScheme.outline)
                : null,
          ),
          child: Icon(
            Icons.restaurant,
            size: 24,
            color: theme.colorScheme.onSurface.withOpacity(0.5), // Changed from withValues
            semanticLabel: '${widget.cartItem.displayName} image', // Changed to displayName
          ),
        ),
        
        const SizedBox(width: 12),
        
        // Item name and details
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Semantics(
                header: true,
                child: Text(
                  widget.cartItem.displayName,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    fontSize: 16 * accessibilityService.textScaleFactor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              
              const SizedBox(height: 4),
              
              // Individual price
              Text(
                '\$${widget.cartItem.itemPrice.toStringAsFixed(2)} each',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontSize: 14 * accessibilityService.textScaleFactor,
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
        
        // Total price
        Semantics(
          label: 'Total price: \$${totalPrice.toStringAsFixed(2)}',
          child: Text(
            '\$${totalPrice.toStringAsFixed(2)}',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 16 * accessibilityService.textScaleFactor,
              color: theme.primaryColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildItemDetails(ThemeData theme, AccessibilityService accessibilityService) {
    // Check if customizations or special instructions exist and are not empty
    final hasCustomizations = widget.cartItem.customizations?.isNotEmpty == true;
    final hasSpecialInstructions = widget.cartItem.specialInstructions?.isNotEmpty == true;

    if (!hasCustomizations && !hasSpecialInstructions) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Customizations
        if (hasCustomizations) ...[
          Semantics(
            label: 'Customizations: ${widget.cartItem.customizations!.values.expand((e) => e).map((e) => e.name).join(', ')}',
            child: Wrap(
              spacing: 8,
              runSpacing: 4,
              children: widget.cartItem.customizations!.entries.expand((entry) => entry.value).map((menuItem) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: accessibilityService.isHighContrastEnabled
                        ? Border.all(color: theme.primaryColor)
                        : null,
                  ),
                  child: Text(
                    menuItem.name, // Access the name from MenuItem
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontSize: 12 * accessibilityService.textScaleFactor,
                      color: theme.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
        
        // Special instructions
        if (hasSpecialInstructions) ...[
          const SizedBox(height: 8),
          Semantics(
            label: 'Special instructions: ${widget.cartItem.specialInstructions}',
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.outline.withOpacity(0.5),
                ),
              ),
              child: Text(
                'Special instructions: ${widget.cartItem.specialInstructions}',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontSize: 12 * accessibilityService.textScaleFactor,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildItemActions(ThemeData theme, AccessibilityService accessibilityService) {
    return Row(
      children: [
        // Quantity controls
        Expanded(
          child: _buildQuantityControls(theme, accessibilityService),
        ),
        
        const SizedBox(width: 16),
        
        // Edit button
        if (widget.onEdit != null) ...[
          WCAGButton(
            type: ButtonType.outlined,
            onPressed: widget.onEdit,
            semanticLabel: 'Edit ${widget.cartItem.displayName}',
            tooltip: 'Edit item customizations',
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.edit,
                  size: 16,
                  semanticLabel: '',
                ),
                const SizedBox(width: 4),
                Text(
                  'Edit',
                  style: TextStyle(
                    fontSize: 14 * accessibilityService.textScaleFactor,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
        ],
        
        // Remove button
        WCAGButton(
          type: ButtonType.outlined,
          onPressed: () {
            _showRemoveConfirmation(accessibilityService);
          },
          semanticLabel: 'Remove ${widget.cartItem.displayName} from cart',
          tooltip: 'Remove item from cart',
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.delete_outline,
                size: 16,
                color: theme.colorScheme.error,
                semanticLabel: '',
              ),
              const SizedBox(width: 4),
              Text(
                'Remove',
                style: TextStyle(
                  fontSize: 14 * accessibilityService.textScaleFactor,
                  color: theme.colorScheme.error,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildQuantityControls(ThemeData theme, AccessibilityService accessibilityService) {
    return Semantics(
      label: 'Quantity controls for ${widget.cartItem.displayName}',
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Decrease quantity button
          Semantics(
            button: true,
            enabled: widget.cartItem.quantity > 1,
            label: 'Decrease quantity, current quantity ${widget.cartItem.quantity}',
            child: SizedBox(
              width: accessibilityService.getMinimumTouchTargetSize(),
              height: accessibilityService.getMinimumTouchTargetSize(),
              child: IconButton(
                onPressed: widget.cartItem.quantity > 1 
                    ? () => _updateQuantity(widget.cartItem.quantity - 1, accessibilityService)
                    : null,
                icon: const Icon(Icons.remove),
                tooltip: 'Decrease quantity',
                style: IconButton.styleFrom(
                  backgroundColor: theme.colorScheme.surface,
                  foregroundColor: theme.colorScheme.onSurface,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(
                      color: theme.colorScheme.outline,
                      width: accessibilityService.isHighContrastEnabled ? 2 : 1,
                    ),
                  ),
                ),
              ),
            ),
          ),
          
          // Quantity display
          Container(
            width: 60,
            height: accessibilityService.getMinimumTouchTargetSize(),
            alignment: Alignment.center,
            child: Semantics(
              label: 'Quantity: ${widget.cartItem.quantity}',
              child: Text(
                '${widget.cartItem.quantity}',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  fontSize: 16 * accessibilityService.textScaleFactor,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          
          // Increase quantity button
          Semantics(
            button: true,
            label: 'Increase quantity, current quantity ${widget.cartItem.quantity}',
            child: SizedBox(
              width: accessibilityService.getMinimumTouchTargetSize(),
              height: accessibilityService.getMinimumTouchTargetSize(),
              child: IconButton(
                onPressed: () => _updateQuantity(widget.cartItem.quantity + 1, accessibilityService),
                icon: const Icon(Icons.add),
                tooltip: 'Increase quantity',
                style: IconButton.styleFrom(
                  backgroundColor: theme.colorScheme.surface,
                  foregroundColor: theme.colorScheme.onSurface,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(
                      color: theme.colorScheme.outline,
                      width: accessibilityService.isHighContrastEnabled ? 2 : 1,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _updateQuantity(int newQuantity, AccessibilityService accessibilityService) {
    widget.onQuantityChanged?.call(newQuantity);
    
    // Announce quantity change
    accessibilityService.announce(
      '${widget.cartItem.displayName} quantity updated to $newQuantity'
    );
  }

  void _showRemoveConfirmation(AccessibilityService accessibilityService) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Remove Item'),
          content: Text('Are you sure you want to remove ${widget.cartItem.displayName} from your cart?'),
          actions: [
            WCAGButton(
              type: ButtonType.text,
              onPressed: () {
                Navigator.of(context).pop();
              },
              semanticLabel: 'Cancel removal',
              child: const Text('Cancel'),
            ),
            WCAGButton(
              onPressed: () {
                Navigator.of(context).pop();
                widget.onRemove?.call();
                accessibilityService.announce(
                  '${widget.cartItem.displayName} removed from cart'
                );
              },
              semanticLabel: 'Confirm removal of ${widget.cartItem.displayName}',
              child: const Text('Remove'),
            ),
          ],
        );
      },
    );
  }
}
