import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:share_plus/share_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';
import '../services/unified_loyalty_service.dart';
import '../widgets/custom_bottom_nav_bar.dart';
import '../services/navigation_service.dart';
import 'social_rewards_privacy_screen.dart';
import 'social_rewards_leaderboard_screen.dart';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// 📸 Social Rewards Screen - Take photos/videos and earn loyalty points!
/// 
/// This screen lets users:
/// 1. Take photos or short videos with their camera
/// 2. Add hashtags and watermarks to their content
/// 3. Share to social media (Instagram, Facebook, TikTok)
/// 4. Earn 10 loyalty points for each share
/// 
/// Perfect for a 10-year-old developer to understand! 🎉
class SocialRewardsScreen extends StatefulWidget {
  const SocialRewardsScreen({super.key});

  @override
  State<SocialRewardsScreen> createState() => _SocialRewardsScreenState();
}

class _SocialRewardsScreenState extends State<SocialRewardsScreen> {
  // 📱 These variables store our app's current state
  final ImagePicker _picker = ImagePicker(); // Helper to access camera
  final UnifiedLoyaltyService _loyaltyService = UnifiedLoyaltyService(); // Points system
  
  // 🖼️ Variables to store the photo/video the user takes
  XFile? _capturedMedia; // The photo or video file
  bool _isVideo = false; // Is it a video (true) or photo (false)?
  bool _isLoading = false; // Are we doing something that takes time?
  
  // 📝 Text editing for hashtags
  final TextEditingController _hashtagController = TextEditingController();
  bool _addWatermark = true; // Should we add our brand watermark?
  
  // 🎨 Photo filter settings (simple brightness adjustment)
  double _brightness = 0.0; // -1.0 (dark) to 1.0 (bright), 0.0 is normal

  @override
  void initState() {
    super.initState();
    // 🏁 Set up default hashtag when screen starts
    _hashtagController.text = '#ChicasChickenBites #QSRRewards';
  }

  @override
  void dispose() {
    // 🧹 Clean up when leaving this screen (prevents memory leaks)
    _hashtagController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          '📸 SOCIAL REWARDS',
          style: TextStyle(
            fontFamily: 'MontserratBlack',
            fontSize: 25,
            fontWeight: FontWeight.w900,
          ),
        ),
        backgroundColor: const Color(0xFFFF5C22), // Chica's orange color
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.leaderboard),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SocialRewardsLeaderboardScreen(),
                ),
              );
            },
            tooltip: 'Leaderboard',
          ),
          IconButton(
            icon: const Icon(Icons.privacy_tip),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SocialRewardsPrivacyScreen(),
                ),
              );
            },
            tooltip: 'Privacy Information',
          ),
        ],
      ),
      body: _capturedMedia == null 
          ? _buildCameraInterface() // Show camera buttons if no photo taken
          : _buildPreviewInterface(), // Show preview if photo was taken
      bottomNavigationBar: CustomBottomNavBar(
        selectedIndex: 1, // Games section (where Social Rewards lives)
        onItemSelected: (index) {
          switch (index) {
            case 0:
              NavigationService.navigateToHome();
              break;
            case 1:
              NavigationService.navigateToScan();
              break;
            case 2:
              NavigationService.navigateToMenu();
              break;
            case 3:
              NavigationService.navigateToCart();
              break;
            case 4:
              NavigationService.navigateToMore();
              break;
          }
        },
        cartService: null, // No cart needed for this screen
      ),
    );
  }

  /// 📷 Camera Interface - The first screen users see
  /// Shows big buttons to take photos or videos
  Widget _buildCameraInterface() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 🎉 Welcome message
          const Icon(
            Icons.camera_alt,
            size: 100,
            color: Color(0xFFFF5C22),
          ),
          const SizedBox(height: 20),
          const Text(
            'Share Your Chica\'s Experience!',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              fontFamily: 'MontserratBlack',
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 10),
          const Text(
            'Take a photo or video of your delicious meal and share it on social media to earn 10 loyalty points!',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),
          
          // 📸 Photo button
          _buildCameraButton(
            icon: Icons.photo_camera,
            label: 'Take Photo',
            onPressed: () => _captureMedia(isVideo: false),
            color: const Color(0xFFFF5C22),
          ),
          
          const SizedBox(height: 20),
          
          // 🎥 Video button
          _buildCameraButton(
            icon: Icons.videocam,
            label: 'Record Video (15s max)',
            onPressed: () => _captureMedia(isVideo: true),
            color: const Color(0xFF4CAF50),
          ),
          
          const SizedBox(height: 40),
          
          // 🔒 Privacy notice
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: const Column(
              children: [
                Icon(Icons.privacy_tip, color: Colors.blue),
                SizedBox(height: 8),
                Text(
                  'Privacy Notice',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'We don\'t store your photos or videos. They are shared directly to your chosen social media platform.',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 🎬 Preview Interface - Shows after taking photo/video
  /// Lets users add hashtags, filters, and share
  Widget _buildPreviewInterface() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 🖼️ Media preview
          _buildMediaPreview(),
          const SizedBox(height: 20),
          
          // 🎨 Simple filter controls (for photos only)
          if (!_isVideo) _buildFilterControls(),
          
          // 📝 Hashtag editor
          _buildHashtagEditor(),
          
          // 🏷️ Watermark toggle
          _buildWatermarkToggle(),
          
          const SizedBox(height: 20),
          
          // 📱 Social media sharing buttons
          _buildSharingButtons(),
          
          const SizedBox(height: 20),
          
          // 🔄 Retake button
          _buildRetakeButton(),
        ],
      ),
    );
  }

  /// 📸 Helper method to build camera buttons
  Widget _buildCameraButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return SizedBox(
      width: double.infinity,
      height: 60,
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : onPressed,
        icon: Icon(icon, size: 28),
        label: Text(
          label,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 4,
        ),
      ),
    );
  }

  /// 🖼️ Shows the captured photo or video
  Widget _buildMediaPreview() {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: _isVideo
            ? _buildVideoPreview()
            : _buildPhotoPreview(),
      ),
    );
  }

  /// 📹 Video preview placeholder
  Widget _buildVideoPreview() {
    return Container(
      color: Colors.black,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.play_circle_outline,
              size: 80,
              color: Colors.white,
            ),
            SizedBox(height: 10),
            Text(
              'Video Ready to Share!',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 📷 Photo preview with brightness filter
  Widget _buildPhotoPreview() {
    return ColorFiltered(
      colorFilter: ColorFilter.matrix(_getBrightnessMatrix()),
      child: Image.file(
        File(_capturedMedia!.path),
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
      ),
    );
  }

  /// 🎨 Simple brightness filter controls
  Widget _buildFilterControls() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '🎨 Photo Filter',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 10),
        Row(
          children: [
            const Icon(Icons.brightness_low),
            Expanded(
              child: Slider(
                value: _brightness,
                min: -0.5,
                max: 0.5,
                divisions: 10,
                activeColor: const Color(0xFFFF5C22),
                onChanged: (value) {
                  setState(() {
                    _brightness = value;
                  });
                },
              ),
            ),
            const Icon(Icons.brightness_high),
          ],
        ),
        const SizedBox(height: 20),
      ],
    );
  }

  /// 📝 Hashtag editor - Let users customize their post text
  Widget _buildHashtagEditor() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '📝 Add Hashtags',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 10),
        TextField(
          controller: _hashtagController,
          maxLines: 2,
          decoration: InputDecoration(
            hintText: 'Add your hashtags here...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFFF5C22)),
            ),
            prefixIcon: const Icon(Icons.tag),
          ),
        ),
        const SizedBox(height: 20),
      ],
    );
  }

  /// 🏷️ Watermark toggle - Add Chica's branding
  Widget _buildWatermarkToggle() {
    return Row(
      children: [
        const Text(
          '🏷️ Add Chica\'s Watermark',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        Switch(
          value: _addWatermark,
          activeColor: const Color(0xFFFF5C22),
          onChanged: (value) {
            setState(() {
              _addWatermark = value;
            });
          },
        ),
      ],
    );
  }

  /// 📱 Social media sharing buttons
  Widget _buildSharingButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '📱 Share & Earn Points!',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 10),
        const Text(
          'Share to any social media app and earn 10 loyalty points!',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 15),

        // Instagram-style button
        _buildSocialButton(
          icon: Icons.camera_alt,
          label: 'Share to Instagram',
          color: const Color(0xFFE4405F),
          onPressed: () => _shareToSocialMedia('Instagram'),
        ),

        const SizedBox(height: 10),

        // Facebook-style button
        _buildSocialButton(
          icon: Icons.facebook,
          label: 'Share to Facebook',
          color: const Color(0xFF1877F2),
          onPressed: () => _shareToSocialMedia('Facebook'),
        ),

        const SizedBox(height: 10),

        // TikTok-style button
        _buildSocialButton(
          icon: Icons.music_video,
          label: 'Share to TikTok',
          color: const Color(0xFF000000),
          onPressed: () => _shareToSocialMedia('TikTok'),
        ),

        const SizedBox(height: 10),

        // General share button
        _buildSocialButton(
          icon: Icons.share,
          label: 'Share to Other Apps',
          color: const Color(0xFF4CAF50),
          onPressed: () => _shareToSocialMedia('Other'),
        ),
      ],
    );
  }

  /// 📱 Helper to build social media buttons
  Widget _buildSocialButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : onPressed,
        icon: Icon(icon),
        label: Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  /// 🔄 Retake button - Start over with new photo/video
  Widget _buildRetakeButton() {
    return OutlinedButton.icon(
      onPressed: _isLoading ? null : _retakeMedia,
      icon: const Icon(Icons.refresh),
      label: const Text('Take New Photo/Video'),
      style: OutlinedButton.styleFrom(
        foregroundColor: const Color(0xFFFF5C22),
        side: const BorderSide(color: Color(0xFFFF5C22)),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(vertical: 15),
      ),
    );
  }

  // 🔧 HELPER METHODS - The "behind the scenes" functions

  /// 📷 Capture media (photo or video) with permission handling
  Future<void> _captureMedia({required bool isVideo}) async {
    try {
      setState(() {
        _isLoading = true;
      });

      // 🔐 Step 1: Ask for camera permission (very important!)
      final permission = await Permission.camera.request();
      if (permission != PermissionStatus.granted) {
        _showPermissionDialog();
        return;
      }

      // 📸 Step 2: Take the photo or video
      XFile? media;
      if (isVideo) {
        // Record video (max 15 seconds)
        media = await _picker.pickVideo(
          source: ImageSource.camera,
          maxDuration: const Duration(seconds: 15),
        );
      } else {
        // Take photo
        media = await _picker.pickImage(
          source: ImageSource.camera,
          imageQuality: 85, // Good quality but not too big
        );
      }

      // 📱 Step 3: Save the media if user didn't cancel
      if (media != null) {
        setState(() {
          _capturedMedia = media;
          _isVideo = isVideo;
        });
      }
    } catch (e) {
      // 🚨 Something went wrong - tell the user nicely
      _showErrorDialog('Oops! Something went wrong with the camera. Please try again.');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 📱 Share to social media and award points
  Future<void> _shareToSocialMedia(String platform) async {
    if (_capturedMedia == null) return;

    try {
      setState(() {
        _isLoading = true;
      });

      // 📝 Prepare the sharing text
      String shareText = _hashtagController.text;
      if (_addWatermark) {
        shareText += '\n\n🍗 Shared from Chica\'s Chicken App!';
      }

      // 📤 Share the media file
      await Share.shareXFiles(
        [_capturedMedia!],
        text: shareText,
        subject: 'Check out my delicious meal from Chica\'s Chicken!',
      );

      // 🏆 Award loyalty points for sharing
      await _awardSharingPoints(platform);

      // 📊 Update leaderboard with new share
      await _updateLeaderboard();

      // 🎉 Show success message
      _showSuccessDialog();

    } catch (e) {
      _showErrorDialog('Could not share to $platform. Make sure the app is installed!');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 🏆 Award points for social media sharing
  Future<void> _awardSharingPoints(String platform) async {
    try {
      await _loyaltyService.awardPointsForGame(
        gameId: 'social_rewards',
        gamePoints: 10, // 10 points per share
        gameScore: 1.0, // Simple score for sharing
        timeSpent: 60, // Approximate time spent
      );
    } catch (e) {
      // Points awarding failed, but sharing succeeded
      debugPrint('Failed to award points: $e');
    }
  }

  /// 🔄 Reset to take new media
  void _retakeMedia() {
    setState(() {
      _capturedMedia = null;
      _isVideo = false;
      _brightness = 0.0;
    });
  }

  /// 🎨 Get brightness adjustment matrix for photo filter
  List<double> _getBrightnessMatrix() {
    return [
      1, 0, 0, 0, _brightness * 255,
      0, 1, 0, 0, _brightness * 255,
      0, 0, 1, 0, _brightness * 255,
      0, 0, 0, 1, 0,
    ];
  }

  /// 🚨 Show permission dialog when camera access is denied
  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('📷 Camera Permission Needed'),
        content: const Text(
          'We need access to your camera to take photos for Social Rewards. '
          'Please allow camera access in your device settings.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings(); // Opens device settings
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFF5C22),
              foregroundColor: Colors.white,
            ),
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  /// 🎉 Show success dialog after sharing
  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🎉 Success!'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 60,
            ),
            SizedBox(height: 16),
            Text(
              'You earned 10 loyalty points for sharing!',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              'Keep sharing to earn more rewards!',
              style: TextStyle(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _retakeMedia(); // Reset for another share
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFF5C22),
              foregroundColor: Colors.white,
            ),
            child: const Text('Share Another!'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  /// 🚨 Show error dialog when something goes wrong
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('😅 Oops!'),
        content: Text(message),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFF5C22),
              foregroundColor: Colors.white,
            ),
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }

  /// 🏆 Update leaderboard when user shares content
  Future<void> _updateLeaderboard() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final leaderboardJson = prefs.getString('social_rewards_leaderboard') ?? '[]';
      final List<dynamic> leaderboardData = json.decode(leaderboardJson);

      // Convert to LeaderboardEntry objects
      List<Map<String, dynamic>> leaderboard = leaderboardData.cast<Map<String, dynamic>>();

      // Find current user (for now, use a simple "You" identifier)
      // In a real app, you'd use the actual user ID from authentication
      const String currentUserName = 'You';

      // Find existing entry or create new one
      int existingIndex = leaderboard.indexWhere((entry) => entry['userName'] == currentUserName);

      if (existingIndex >= 0) {
        // Update existing entry
        leaderboard[existingIndex]['sharesCount'] = (leaderboard[existingIndex]['sharesCount'] ?? 0) + 1;
        leaderboard[existingIndex]['pointsEarned'] = (leaderboard[existingIndex]['pointsEarned'] ?? 0) + 10;
        leaderboard[existingIndex]['lastShareDate'] = DateTime.now().toIso8601String();
      } else {
        // Create new entry
        leaderboard.add({
          'userName': currentUserName,
          'sharesCount': 1,
          'pointsEarned': 10,
          'joinDate': DateTime.now().toIso8601String(),
          'lastShareDate': DateTime.now().toIso8601String(),
        });
      }

      // Save updated leaderboard
      await prefs.setString('social_rewards_leaderboard', json.encode(leaderboard));

      debugPrint('🏆 Leaderboard updated successfully!');
    } catch (e) {
      debugPrint('🚨 Error updating leaderboard: $e');
      // Don't show error to user - leaderboard is optional feature
    }
  }

  /// 🧭 Handle bottom navigation
  void _handleNavigation(int index) {
    switch (index) {
      case 0:
        NavigationService.navigateToHome();
        break;
      case 1:
        // Already on games section, go back to games hub
        Navigator.pop(context);
        break;
      case 2:
        NavigationService.navigateToMenu();
        break;
      case 3:
        NavigationService.navigateToCart();
        break;
      case 4:
        NavigationService.navigateToMore();
        break;
    }
  }
}
