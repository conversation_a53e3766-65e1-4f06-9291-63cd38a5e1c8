import 'package:flutter/material.dart';
import '../services/accessibility_service.dart';

/// 🎨 WCAG 2.1 AA Compliant Theme for CHICA'S Chicken
/// Provides accessible color schemes, typography, and component styles
class AccessibleTheme {
  static const Color _chicaOrange = Color(0xFFFF5C22);
  static const Color _chicaRed = Color(0xFF9B1C24);
  static const Color _chicaGold = Color(0xFFB89B5E);

  /// Light theme with WCAG 2.1 AA compliance
  static ThemeData lightTheme(AccessibilityService accessibilityService) {
    final baseTheme = ThemeData.light();
    
    return baseTheme.copyWith(
      // Color Scheme with proper contrast ratios
      colorScheme: _buildLightColorScheme(accessibilityService),
      
      // Typography with accessibility considerations
      textTheme: _buildAccessibleTextTheme(baseTheme.textTheme, accessibilityService, false),
      
      // Component themes
      elevatedButtonTheme: _buildElevatedButtonTheme(accessibilityService, false),
      outlinedButtonTheme: _buildOutlinedButtonTheme(accessibilityService, false),
      textButtonTheme: _buildTextButtonTheme(accessibilityService, false),
      inputDecorationTheme: _buildInputDecorationTheme(accessibilityService, false),
      cardTheme: _buildCardTheme(accessibilityService, false),
      appBarTheme: _buildAppBarTheme(accessibilityService, false),
      
      // Focus theme for keyboard navigation
      focusColor: _chicaOrange.withOpacity(0.3),
      
      // Accessibility-specific properties
      visualDensity: accessibilityService.isLargeTextEnabled 
          ? VisualDensity.comfortable 
          : VisualDensity.standard,
    );
  }

  /// Dark theme with WCAG 2.1 AA compliance
  static ThemeData darkTheme(AccessibilityService accessibilityService) {
    final baseTheme = ThemeData.dark();
    
    return baseTheme.copyWith(
      // Color Scheme with proper contrast ratios
      colorScheme: _buildDarkColorScheme(accessibilityService),
      
      // Typography with accessibility considerations
      textTheme: _buildAccessibleTextTheme(baseTheme.textTheme, accessibilityService, true),
      
      // Component themes
      elevatedButtonTheme: _buildElevatedButtonTheme(accessibilityService, true),
      outlinedButtonTheme: _buildOutlinedButtonTheme(accessibilityService, true),
      textButtonTheme: _buildTextButtonTheme(accessibilityService, true),
      inputDecorationTheme: _buildInputDecorationTheme(accessibilityService, true),
      cardTheme: _buildCardTheme(accessibilityService, true),
      appBarTheme: _buildAppBarTheme(accessibilityService, true),
      
      // Focus theme for keyboard navigation
      focusColor: _chicaOrange.withOpacity(0.4),
      
      // Accessibility-specific properties
      visualDensity: accessibilityService.isLargeTextEnabled 
          ? VisualDensity.comfortable 
          : VisualDensity.standard,
    );
  }

  /// Build light color scheme with WCAG compliance
  static ColorScheme _buildLightColorScheme(AccessibilityService accessibilityService) {
    if (accessibilityService.isHighContrastEnabled) {
      return const ColorScheme.light(
        primary: Colors.black,
        onPrimary: Colors.white,
        secondary: Colors.black,
        onSecondary: Colors.white,
        surface: Colors.white,
        onSurface: Colors.black,
        error: Color(0xFFD32F2F),
        onError: Colors.white,
      );
    }
    
    return ColorScheme.light(
      primary: _chicaOrange,
      onPrimary: Colors.white,
      secondary: _chicaRed,
      onSecondary: Colors.white,
      tertiary: _chicaGold,
      onTertiary: Colors.white,
      surface: Colors.white,
      onSurface: Colors.black87,
      error: const Color(0xFFD32F2F),
      onError: Colors.white,
      outline: Colors.grey.shade400,
    );
  }

  /// Build dark color scheme with WCAG compliance
  static ColorScheme _buildDarkColorScheme(AccessibilityService accessibilityService) {
    if (accessibilityService.isHighContrastEnabled) {
      return const ColorScheme.dark(
        primary: Colors.white,
        onPrimary: Colors.black,
        secondary: Colors.white,
        onSecondary: Colors.black,
        surface: Colors.black,
        onSurface: Colors.white,
        error: Color(0xFFFF5252),
        onError: Colors.black,
      );
    }
    
    return ColorScheme.dark(
      primary: _chicaOrange,
      onPrimary: Colors.white,
      secondary: _chicaRed,
      onSecondary: Colors.white,
      tertiary: _chicaGold,
      onTertiary: Colors.black,
      surface: const Color(0xFF1E1E1E),
      onSurface: Colors.white,
      error: const Color(0xFFFF5252),
      onError: Colors.black,
      outline: Colors.grey.shade600,
    );
  }

  /// Build accessible text theme
  static TextTheme _buildAccessibleTextTheme(
    TextTheme baseTheme, 
    AccessibilityService accessibilityService, 
    bool isDark
  ) {
    final scaleFactor = accessibilityService.textScaleFactor;
    final baseColor = isDark ? Colors.white : Colors.black87;
    
    return baseTheme.copyWith(
      displayLarge: baseTheme.displayLarge?.copyWith(
        fontSize: (baseTheme.displayLarge?.fontSize ?? 57) * scaleFactor,
        color: baseColor,
        fontWeight: FontWeight.w400,
      ),
      displayMedium: baseTheme.displayMedium?.copyWith(
        fontSize: (baseTheme.displayMedium?.fontSize ?? 45) * scaleFactor,
        color: baseColor,
        fontWeight: FontWeight.w400,
      ),
      displaySmall: baseTheme.displaySmall?.copyWith(
        fontSize: (baseTheme.displaySmall?.fontSize ?? 36) * scaleFactor,
        color: baseColor,
        fontWeight: FontWeight.w400,
      ),
      headlineLarge: baseTheme.headlineLarge?.copyWith(
        fontSize: (baseTheme.headlineLarge?.fontSize ?? 32) * scaleFactor,
        color: baseColor,
        fontWeight: FontWeight.w400,
      ),
      headlineMedium: baseTheme.headlineMedium?.copyWith(
        fontSize: (baseTheme.headlineMedium?.fontSize ?? 28) * scaleFactor,
        color: baseColor,
        fontWeight: FontWeight.w400,
      ),
      headlineSmall: baseTheme.headlineSmall?.copyWith(
        fontSize: (baseTheme.headlineSmall?.fontSize ?? 24) * scaleFactor,
        color: baseColor,
        fontWeight: FontWeight.w400,
      ),
      titleLarge: baseTheme.titleLarge?.copyWith(
        fontSize: (baseTheme.titleLarge?.fontSize ?? 22) * scaleFactor,
        color: baseColor,
        fontWeight: FontWeight.w500,
      ),
      titleMedium: baseTheme.titleMedium?.copyWith(
        fontSize: (baseTheme.titleMedium?.fontSize ?? 16) * scaleFactor,
        color: baseColor,
        fontWeight: FontWeight.w500,
      ),
      titleSmall: baseTheme.titleSmall?.copyWith(
        fontSize: (baseTheme.titleSmall?.fontSize ?? 14) * scaleFactor,
        color: baseColor,
        fontWeight: FontWeight.w500,
      ),
      bodyLarge: baseTheme.bodyLarge?.copyWith(
        fontSize: (baseTheme.bodyLarge?.fontSize ?? 16) * scaleFactor,
        color: baseColor,
        fontWeight: FontWeight.w400,
      ),
      bodyMedium: baseTheme.bodyMedium?.copyWith(
        fontSize: (baseTheme.bodyMedium?.fontSize ?? 14) * scaleFactor,
        color: baseColor,
        fontWeight: FontWeight.w400,
      ),
      bodySmall: baseTheme.bodySmall?.copyWith(
        fontSize: (baseTheme.bodySmall?.fontSize ?? 12) * scaleFactor,
        color: baseColor,
        fontWeight: FontWeight.w400,
      ),
      labelLarge: baseTheme.labelLarge?.copyWith(
        fontSize: (baseTheme.labelLarge?.fontSize ?? 14) * scaleFactor,
        color: baseColor,
        fontWeight: FontWeight.w500,
      ),
      labelMedium: baseTheme.labelMedium?.copyWith(
        fontSize: (baseTheme.labelMedium?.fontSize ?? 12) * scaleFactor,
        color: baseColor,
        fontWeight: FontWeight.w500,
      ),
      labelSmall: baseTheme.labelSmall?.copyWith(
        fontSize: (baseTheme.labelSmall?.fontSize ?? 11) * scaleFactor,
        color: baseColor,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  /// Build elevated button theme with accessibility
  static ElevatedButtonThemeData _buildElevatedButtonTheme(
    AccessibilityService accessibilityService, 
    bool isDark
  ) {
    final minSize = Size(
      accessibilityService.getMinimumTouchTargetSize(),
      accessibilityService.getMinimumTouchTargetSize(),
    );
    
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        minimumSize: minSize,
        padding: accessibilityService.getRecommendedPadding(),
        textStyle: TextStyle(
          fontSize: 16 * accessibilityService.textScaleFactor,
          fontWeight: FontWeight.w500,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Build outlined button theme with accessibility
  static OutlinedButtonThemeData _buildOutlinedButtonTheme(
    AccessibilityService accessibilityService, 
    bool isDark
  ) {
    final minSize = Size(
      accessibilityService.getMinimumTouchTargetSize(),
      accessibilityService.getMinimumTouchTargetSize(),
    );
    
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        minimumSize: minSize,
        padding: accessibilityService.getRecommendedPadding(),
        textStyle: TextStyle(
          fontSize: 16 * accessibilityService.textScaleFactor,
          fontWeight: FontWeight.w500,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        side: BorderSide(
          width: accessibilityService.isHighContrastEnabled ? 2 : 1,
        ),
      ),
    );
  }

  /// Build text button theme with accessibility
  static TextButtonThemeData _buildTextButtonTheme(
    AccessibilityService accessibilityService, 
    bool isDark
  ) {
    final minSize = Size(
      accessibilityService.getMinimumTouchTargetSize(),
      accessibilityService.getMinimumTouchTargetSize(),
    );
    
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        minimumSize: minSize,
        padding: accessibilityService.getRecommendedPadding(),
        textStyle: TextStyle(
          fontSize: 16 * accessibilityService.textScaleFactor,
          fontWeight: FontWeight.w500,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Build input decoration theme with accessibility
  static InputDecorationTheme _buildInputDecorationTheme(
    AccessibilityService accessibilityService, 
    bool isDark
  ) {
    return InputDecorationTheme(
      contentPadding: accessibilityService.getRecommendedPadding(),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(
          width: accessibilityService.isHighContrastEnabled ? 2 : 1,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(
          color: _chicaOrange,
          width: 2,
        ),
      ),
      labelStyle: TextStyle(
        fontSize: 16 * accessibilityService.textScaleFactor,
      ),
      hintStyle: TextStyle(
        fontSize: 16 * accessibilityService.textScaleFactor,
      ),
    );
  }

  /// Build card theme with accessibility
  static CardThemeData _buildCardTheme(
    AccessibilityService accessibilityService, 
    bool isDark
  ) {
    return CardThemeData(
      margin: accessibilityService.getRecommendedPadding(),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: accessibilityService.isHighContrastEnabled 
            ? BorderSide(
                color: isDark ? Colors.white : Colors.black,
                width: 1,
              )
            : BorderSide.none,
      ),
      elevation: accessibilityService.isReduceMotionEnabled ? 1 : 4,
    );
  }

  /// Build app bar theme with accessibility
  static AppBarTheme _buildAppBarTheme(
    AccessibilityService accessibilityService, 
    bool isDark
  ) {
    return AppBarTheme(
      titleTextStyle: TextStyle(
        fontSize: 20 * accessibilityService.textScaleFactor,
        fontWeight: FontWeight.w500,
        color: Colors.white,
      ),
      toolbarHeight: accessibilityService.isLargeTextEnabled ? 64 : 56,
    );
  }
}
