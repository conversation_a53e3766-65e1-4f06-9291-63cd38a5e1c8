import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';
import 'package:flutter/services.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:vibration/vibration.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 🌟 Comprehensive Accessibility Service for WCAG 2.1 AA Compliance
/// Provides centralized accessibility features for CHICA'S Chicken app

final accessibilityServiceProvider = ChangeNotifierProvider<AccessibilityService>((ref) {
  final service = AccessibilityService();
  service.initialize(); // Initialize the service when the provider is created
  return service;
});

class AccessibilityService extends ChangeNotifier {
  // Text-to-Speech Engine
  final FlutterTts _flutterTts = FlutterTts();
  
  // Accessibility State
  bool _isScreenReaderEnabled = false;
  bool _isHighContrastEnabled = false;
  bool _isLargeTextEnabled = false;
  bool _isReduceMotionEnabled = false;
  bool _isTtsEnabled = true;
  bool _isHapticFeedbackEnabled = true;
  double _textScaleFactor = 1.0;
  
  // Getters
  bool get isScreenReaderEnabled => _isScreenReaderEnabled;
  bool get isHighContrastEnabled => _isHighContrastEnabled;
  bool get isLargeTextEnabled => _isLargeTextEnabled;
  bool get isReduceMotionEnabled => _isReduceMotionEnabled;
  bool get isTtsEnabled => _isTtsEnabled;
  bool get isHapticFeedbackEnabled => _isHapticFeedbackEnabled;
  double get textScaleFactor => _textScaleFactor;

  /// Initialize accessibility service
  Future<void> initialize() async {
    await _initializeTts();
    await _checkSystemAccessibilitySettings();
  }

  /// Initialize Text-to-Speech
  Future<void> _initializeTts() async {
    try {
      await _flutterTts.setLanguage('en-US');
      await _flutterTts.setSpeechRate(0.5);
      await _flutterTts.setVolume(1.0);
      await _flutterTts.setPitch(1.0);
    } catch (e) {
      debugPrint('TTS initialization failed: $e');
    }
  }

  /// Check system accessibility settings
  Future<void> _checkSystemAccessibilitySettings() async {
    // This would typically check system settings
    // For now, we'll use MediaQuery in the widget context
  }

  /// Update accessibility settings from MediaQuery
  void updateFromMediaQuery(MediaQueryData mediaQuery) {
    final oldScreenReader = _isScreenReaderEnabled;
    final oldHighContrast = _isHighContrastEnabled;
    final oldTextScale = _textScaleFactor;
    
    _isScreenReaderEnabled = mediaQuery.accessibleNavigation;
    _isHighContrastEnabled = mediaQuery.highContrast;
    _isReduceMotionEnabled = mediaQuery.disableAnimations;
    _textScaleFactor = mediaQuery.textScaleFactor;
    _isLargeTextEnabled = _textScaleFactor > 1.3;

    if (oldScreenReader != _isScreenReaderEnabled ||
        oldHighContrast != _isHighContrastEnabled ||
        oldTextScale != _textScaleFactor) {
      notifyListeners();
    }
  }

  /// Announce message to screen readers
  Future<void> announce(String message, {bool interrupt = false}) async {
    if (!_isScreenReaderEnabled && !_isTtsEnabled) return;
    
    try {
      if (interrupt) {
        await _flutterTts.stop();
      }
      
      // Use SemanticsService for screen readers
      SemanticsService.announce(message, TextDirection.ltr);
      
      // Also use TTS if enabled
      if (_isTtsEnabled) {
        await _flutterTts.speak(message);
      }
    } catch (e) {
      debugPrint('Announcement failed: $e');
    }
  }

  /// Provide haptic feedback
  Future<void> hapticFeedback() async {
    if (!_isHapticFeedbackEnabled) return;

    try {
      if (await Vibration.hasVibrator() ?? false) {
        HapticFeedback.lightImpact();
      }
    } catch (e) {
      debugPrint('Haptic feedback failed: $e');
    }
  }

  /// Toggle TTS
  void toggleTts() {
    _isTtsEnabled = !_isTtsEnabled;
    notifyListeners();
  }

  /// Toggle haptic feedback
  void toggleHapticFeedback() {
    _isHapticFeedbackEnabled = !_isHapticFeedbackEnabled;
    notifyListeners();
  }

  /// Set text scale factor
  void setTextScaleFactor(double factor) {
    _textScaleFactor = factor.clamp(0.8, 3.0);
    _isLargeTextEnabled = _textScaleFactor > 1.3;
    notifyListeners();
  }

  /// Get minimum touch target size based on accessibility settings
  double getMinimumTouchTargetSize() {
    if (_isLargeTextEnabled) {
      return 56.0; // Larger for accessibility
    }
    return 48.0; // Standard Material Design
  }

  /// Get recommended padding based on accessibility settings
  EdgeInsets getRecommendedPadding() {
    if (_isLargeTextEnabled) {
      return const EdgeInsets.all(20.0);
    }
    return const EdgeInsets.all(16.0);
  }

  /// Check if color contrast meets WCAG AA standards
  bool meetsContrastRequirements(Color foreground, Color background, {bool isLargeText = false}) {
    final contrastRatio = _calculateContrastRatio(foreground, background);
    final requiredRatio = isLargeText ? 3.0 : 4.5;
    return contrastRatio >= requiredRatio;
  }

  /// Calculate color contrast ratio
  double _calculateContrastRatio(Color color1, Color color2) {
    final luminance1 = _calculateLuminance(color1);
    final luminance2 = _calculateLuminance(color2);
    
    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Calculate relative luminance
  double _calculateLuminance(Color color) {
    final r = _linearizeColorComponent(color.red / 255.0);
    final g = _linearizeColorComponent(color.green / 255.0);
    final b = _linearizeColorComponent(color.blue / 255.0);
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /// Linearize color component for luminance calculation
  double _linearizeColorComponent(double component) {
    if (component <= 0.03928) {
      return component / 12.92;
    } else {
      return ((component + 0.055) / 1.055).pow(2.4);
    }
  }

  /// Get accessible color based on contrast requirements
  Color getAccessibleColor(Color background, {bool isLargeText = false}) {
    if (_isHighContrastEnabled) {
      // Use high contrast colors
      final luminance = _calculateLuminance(background);
      return luminance > 0.5 ? Colors.black : Colors.white;
    }
    
    // Check if default colors meet requirements
    const defaultLight = Colors.black87;
    const defaultDark = Colors.white;
    
    if (meetsContrastRequirements(defaultLight, background, isLargeText: isLargeText)) {
      return defaultLight;
    } else if (meetsContrastRequirements(defaultDark, background, isLargeText: isLargeText)) {
      return defaultDark;
    }
    
    // Fallback to high contrast
    final luminance = _calculateLuminance(background);
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  /// Dispose resources
  @override
  void dispose() {
    _flutterTts.stop();
    super.dispose();
  }
}

/// Extension for double to add pow method
extension DoubleExtension on double {
  double pow(double exponent) {
    return math.pow(this, exponent).toDouble();
  }
}
