import 'package:flutter/material.dart';
import '../services/language_service.dart';
import '../services/auth_service.dart';
import '../widgets/accessible_text.dart';
import '../screens/account_screen.dart';
import '../screens/favorites_screen.dart';
import '../screens/settings_screen.dart';
import '../screens/coming_soon_screen.dart';
import '../screens/auth_required_wrapper.dart';
import '../screens/support_screen.dart';
import '../screens/terms_of_use_screen.dart';
import '../screens/privacy_policy_screen.dart';
import '../models/language.dart';

class NavigationMenuDrawer extends StatelessWidget {
  const NavigationMenuDrawer({super.key});

  void _navigateToComingSoon(BuildContext context, String featureName) {
    Navigator.pop(context);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ComingSoonScreen(featureName: featureName),
      ),
    );
  }

  void _navigateToTermsOfUse(BuildContext context) {
    Navigator.pop(context);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const TermsOfUseScreen(),
      ),
    );
  }

  void _navigateToPrivacyPolicy(BuildContext context) {
    Navigator.pop(context);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const PrivacyPolicyScreen(),
      ),
    );
  }

  void _navigateToSupport(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SupportScreen(),
      ),
    );
  }

  void _navigateToLogin(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const LoginScreen(),
      ),
    );
  }

  void _navigateToSignup(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SignupScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: Theme.of(context).colorScheme.surface,
      child: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return ListView(
              padding: EdgeInsets.zero,
              children: [
                // Header with close button
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'NAVIGATION MENU',
                        style: TextStyle(
                          fontFamily: 'SofiaRoughBlackThree',
                          fontSize: 25,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      IconButton(
                        icon: Icon(
                          Icons.close,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        onPressed: () => Navigator.pop(context),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                ),
                Divider(height: 1, color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.2)),

                // Chica's Rewards Section
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.star, color: Theme.of(context).colorScheme.primary),
                          const SizedBox(width: 8),
                          Text(
                            "CHICA'S Rewards",
                            style: TextStyle(
                              fontFamily: 'SofiaRoughBlackThree',
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Save your order history and information for a faster checkout.',
                        style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
                      ),
                      const SizedBox(height: 16),
                      ConstrainedBox(
                        constraints: BoxConstraints(
                          maxWidth: constraints.maxWidth - 32,
                        ),
                        child: ElevatedButton(
                          onPressed: () => _navigateToLogin(context),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).colorScheme.primary,
                            foregroundColor: Theme.of(context).colorScheme.onPrimary,
                            minimumSize: const Size.fromHeight(80),
                          ),
                          child: const Text('Log In'),
                        ),
                      ),
                      const SizedBox(height: 8),
                      ConstrainedBox(
                        constraints: BoxConstraints(
                          maxWidth: constraints.maxWidth - 32,
                        ),
                        child: OutlinedButton(
                          onPressed: () => _navigateToSignup(context),
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(color: Theme.of(context).colorScheme.primary),
                            foregroundColor: Theme.of(context).colorScheme.primary,
                            minimumSize: const Size.fromHeight(80),
                          ),
                          child: const Text(
                            'Sign Up',
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Divider(height: 1, color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.2)),

                // Language Selection
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Consumer<LanguageService>(
                          builder: (context, languageService, child) {
                            return TextButton(
                              onPressed: () {
                                languageService.setLanguage(Language.french);
                                Navigator.pop(context); // Close drawer after selection
                              },
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                backgroundColor: languageService.language == Language.french
                                    ? Theme.of(context).colorScheme.primary.withAlpha((255 * 0.1).round())
                                    : Colors.transparent,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  side: BorderSide(
                                    color: languageService.language == Language.french
                                        ? Theme.of(context).colorScheme.primary
                                        : Colors.transparent,
                                  ),
                                ),
                              ),
                              child: AccessibleText(
                                languageService.getLanguageDisplayName(Language.french),
                                style: TextStyle(
                                  color: languageService.language == Language.french
                                      ? Theme.of(context).colorScheme.primary
                                      : Theme.of(context).colorScheme.onSurface,
                                  fontWeight: languageService.language == Language.french
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Consumer<LanguageService>(
                          builder: (context, languageService, child) {
                            return TextButton(
                              onPressed: () {
                                languageService.setLanguage(Language.english);
                                Navigator.pop(context); // Close drawer after selection
                              },
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 24),
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                backgroundColor: languageService.language == Language.english
                                    ? Theme.of(context).colorScheme.primary.withAlpha((255 * 0.1).round())
                                    : Colors.transparent,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  side: BorderSide(
                                    color: languageService.language == Language.english
                                        ? Theme.of(context).colorScheme.primary
                                        : Colors.transparent,
                                  ),
                                ),
                              ),
                              child: AccessibleText(
                                languageService.getLanguageDisplayName(Language.english),
                                style: TextStyle(
                                  color: languageService.language == Language.english
                                      ? Theme.of(context).colorScheme.primary
                                      : Theme.of(context).colorScheme.onSurface,
                                  fontWeight: languageService.language == Language.english
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),

                // Menu Items - Using constrained height to prevent overflow
                ...ListTile.divideTiles(
                  context: context,
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.2),
                  tiles: [
                    ListTile(
                      leading: Icon(Icons.person_outline, color: Theme.of(context).colorScheme.onSurface),
                      title: AccessibleText(
                        'My Account',
                        style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => AuthRequiredWrapper(
                              featureName: 'My Account',
                              message: 'Sign in to view your account details, order history, and manage your preferences.',
                              child: AccountScreen(),
                            ),
                          ),
                        );
                      },
                    ),
                    ListTile(
                      leading: Icon(Icons.favorite_outline, color: Theme.of(context).colorScheme.onSurface),
                      title: AccessibleText(
                        'Favorites',
                        style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
                      ),
                      trailing: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.secondary,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'NEW',
                          style: TextStyle(color: Theme.of(context).colorScheme.onSecondary, fontSize: 12),
                        ),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => AuthRequiredWrapper(
                              featureName: 'Favorites',
                              message: 'Sign in to save and view your favorite menu items.',
                              child: FavoritesScreen(),
                            ),
                          ),
                        );
                      },
                    ),

                    ListTile(
                      leading: Icon(Icons.location_on_outlined, color: Theme.of(context).colorScheme.onSurface),
                      title: AccessibleText(
                        'Find a Restaurant',
                        style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
                      ),
                      onTap: () => _navigateToComingSoon(context, 'Restaurant Locator'),
                    ),
                    ListTile(
                      leading: Icon(Icons.card_giftcard, color: Theme.of(context).colorScheme.onSurface),
                      title: AccessibleText(
                        'Rewards Program',
                        style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => AuthRequiredWrapper(
                              featureName: 'Rewards Program',
                              message: 'Sign in to view your reward points, tier status, and available rewards.',
                              child: ComingSoonScreen(featureName: 'Rewards Program'),
                            ),
                          ),
                        );
                      },
                    ),
                    ListTile(
                      leading: Icon(Icons.local_offer_outlined, color: Theme.of(context).colorScheme.onSurface),
                      title: AccessibleText(
                        'Catering',
                        style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
                      ),
                      onTap: () => _navigateToComingSoon(context, 'Catering'),
                    ),
                    ListTile(
                      leading: Icon(Icons.card_giftcard, color: Theme.of(context).colorScheme.onSurface),
                      title: AccessibleText(
                        'Gift Cards',
                        style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
                      ),
                      onTap: () => _navigateToComingSoon(context, 'Gift Cards'),
                    ),
                    ListTile(
                      leading: Icon(Icons.help_outline, color: Theme.of(context).colorScheme.onSurface),
                      title: AccessibleText(
                        'Support & FAQ',
                        style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        _navigateToSupport(context);
                      },
                    ),
                    ListTile(
                      leading: Icon(Icons.notifications_outlined, color: Theme.of(context).colorScheme.onSurface),
                      title: AccessibleText(
                        'Notification Settings',
                        style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, '/notification-settings');
                      },
                    ),
                    ListTile(
                      leading: Icon(Icons.settings_outlined, color: Theme.of(context).colorScheme.onSurface),
                      title: AccessibleText(
                        'Settings',
                        style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
                      ),
                      trailing: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.tertiary,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'NEW',
                          style: TextStyle(color: Theme.of(context).colorScheme.onTertiary, fontSize: 12),
                        ),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const SettingsScreen(),
                          ),
                        );
                      },
                    ),
                    // 🚀 Developer features removed for production
                  ],
                ),

                const SizedBox(height: 16),

                // Legal Terms & Policies Section
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      AccessibleText(
                        'LEGAL TERMS & POLICIES',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      InkWell(
                        onTap: () => _navigateToTermsOfUse(context),
                        child: Padding(
                          padding: EdgeInsets.symmetric(vertical: 10.0),
                          child: AccessibleText(
                            'Terms of Use',
                            style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () => _navigateToPrivacyPolicy(context),
                        child: Padding(
                          padding: EdgeInsets.symmetric(vertical: 8.0),
                          child: AccessibleText(
                            'Privacy Policy',
                            style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],
            );
          },
        ),
      ),
    );
  }

}


