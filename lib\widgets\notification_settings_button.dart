// 🔔 Notification Settings Button Widget
// This is a simple button that can be added anywhere in your app
// to let users access notification settings quickly!

import 'package:flutter/material.dart';

/// 🔔 Notification Settings Button
/// A simple button widget that navigates to notification settings
/// Can be easily added to any screen, settings page, or profile section
class NotificationSettingsButton extends StatelessWidget {
  final String? customText;
  final IconData? customIcon;
  final bool showAsCard;
  final VoidCallback? onTap;

  const NotificationSettingsButton({
    super.key,
    this.customText,
    this.customIcon,
    this.showAsCard = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (showAsCard) {
      return _buildCardStyle(context);
    } else {
      return _buildListTileStyle(context);
    }
  }

  /// 🎴 Build card-style button
  Widget _buildCardStyle(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _handleTap(context),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  customIcon ?? Icons.notifications_active,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      customText ?? 'Notification Settings',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      'Manage your notification preferences',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 📋 Build list tile style button
  Widget _buildListTileStyle(BuildContext context) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          customIcon ?? Icons.notifications_active,
          color: Theme.of(context).primaryColor,
          size: 20,
        ),
      ),
      title: Text(
        customText ?? 'Notification Settings',
        style: const TextStyle(fontWeight: FontWeight.w500),
      ),
      subtitle: const Text('Manage your notification preferences'),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () => _handleTap(context),
    );
  }

  /// 🎯 Handle button tap
  void _handleTap(BuildContext context) {
    if (onTap != null) {
      onTap!();
    } else {
      // Navigate to notification settings screen
      Navigator.of(context).pushNamed('/notification-settings');
    }
  }
}

/// 🔔 Quick Notification Toggle Widget
/// A simple switch that can be embedded anywhere to quickly toggle notifications
class QuickNotificationToggle extends StatefulWidget {
  final String? label;
  final Function(bool)? onChanged;

  const QuickNotificationToggle({
    super.key,
    this.label,
    this.onChanged,
  });

  @override
  State<QuickNotificationToggle> createState() => _QuickNotificationToggleState();
}

class _QuickNotificationToggleState extends State<QuickNotificationToggle> {
  bool _isEnabled = true;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCurrentSetting();
  }

  /// 📊 Load current notification setting
  Future<void> _loadCurrentSetting() async {
    // You can implement this to load from your notification service
    // For now, we'll use a default value
    setState(() {
      _isEnabled = true; // Default to enabled
    });
  }

  /// 💾 Handle toggle change
  Future<void> _handleToggle(bool value) async {
    setState(() => _isLoading = true);
    
    try {
      // Here you would call your notification service
      // await NotificationService().setNotificationsEnabled(value);
      
      setState(() {
        _isEnabled = value;
        _isLoading = false;
      });
      
      if (widget.onChanged != null) {
        widget.onChanged!(value);
      }
      
      // Show feedback to user
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(value 
            ? 'Notifications enabled!' 
            : 'Notifications disabled'),
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (error) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to update notification settings'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Icon(
        _isEnabled ? Icons.notifications_active : Icons.notifications_off,
        color: _isEnabled ? Theme.of(context).primaryColor : Colors.grey,
      ),
      title: Text(widget.label ?? 'Daily Reminders'),
      subtitle: Text(_isEnabled 
        ? 'Get daily feedback reminders at 6 PM' 
        : 'Notifications are disabled'),
      trailing: _isLoading
        ? const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          )
        : Switch(
            value: _isEnabled,
            onChanged: _handleToggle,
            activeColor: Theme.of(context).primaryColor,
          ),
    );
  }
}

/// 🧪 Test Notification Button
/// A simple button to send test notifications for debugging
class TestNotificationButton extends StatelessWidget {
  final String? buttonText;
  final VoidCallback? onPressed;

  const TestNotificationButton({
    super.key,
    this.buttonText,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: onPressed ?? () => _sendTestNotification(context),
      icon: const Icon(Icons.send),
      label: Text(buttonText ?? 'Send Test Notification'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 🧪 Send test notification
  void _sendTestNotification(BuildContext context) {
    // Here you would call your notification service
    // NotificationService().sendTestNotification();
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Test notification sent! Check your notification panel.'),
        duration: Duration(seconds: 3),
      ),
    );
  }
}
